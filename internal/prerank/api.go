package prerank

import (
	"fmt"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/prerank/entity"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/prerank/service"
	proto_prerank "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/prerank"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
	"github.com/bytedance/sonic"
	"github.com/go-playground/validator/v10"
)

var sonicAPI sonic.API

type prerank struct {
	validate *validator.Validate
}

func (r *prerank) RankHandler(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_prerank.Payload]) *pandora_proto.PandoraResponseMessage[proto_prerank.Payload] {
	now := time.Now()
	span := ctx.RootSpan().AddSpan("rank-handler")
	defer span.Finish()
	span.TraceInfo("query_num", len(req.Payload.Data))

	resp := proto_prerank.PrerankAPI.NewPandoraResponseMessage()

	// 请求参数校验
	if err := r.validate.Struct(req); err != nil {
		return fillHeader(resp, selferrors.CommonError_InvalidInput.Detaild(err.Error()))
	}

	ctx.Store().SetData(entity.KeyTag, req.Header.Tag)

	// 调用prerank服务
	if err := service.Service.ProcessReq(ctx, span, &req.Payload); err != nil {
		if selfError, ok := err.(*errtypes.SelfError); ok {
			return fillHeader(resp, selfError)
		}
		return fillHeader(resp, selferrors.PrerankError_InferFailed.Detaild(err.Error()))
	}

	resp.Payload = req.Payload
	resp.Header.Success = fmt.Sprintf("success, cost: %dms", time.Since(now).Milliseconds())
	return resp
}

func fillHeader(resp *pandora_proto.PandoraResponseMessage[proto_prerank.Payload], err *errtypes.SelfError) *pandora_proto.PandoraResponseMessage[proto_prerank.Payload] {
	resp.Header.Code = err.Code()
	resp.Header.Success = err.String()
	return resp
}

func sonicUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[proto_prerank.Payload]) error {
	return sonicAPI.Unmarshal(out, req)
}

// Init 服务初始化
func Init() error {
	// 添加自定义配置
	if err := goboot.RegisterCustomMultiModule(entity.ModelFactory); err != nil {
		panic(fmt.Sprintf("register custom module failed, error: %s", err.Error()))
	}

	// 检查自定义配置是否正确加载
	goboot.GetCustomModule(entity.ModelFactory).Must()

	// tokenizer_v2依赖是否加载
	tokenizer_v2.G_tokenizer.Must()

	// asesdk依赖是否加载
	goboot.AseSdk().Must()

	// 服务初始化
	if err := service.Service.Init(); err != nil {
		return err
	}

	sonicAPI = sonic.Config{UseInt64: true}.Froze()

	prerankInst := &prerank{
		validate: validator.New(validator.WithRequiredStructEnabled()),
	}

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/prerank/api/v2", proto_prerank.PrerankAPI.GinWrapper().SetHandler(prerankInst.RankHandler).SetRequestUnmarshal(sonicUnmarshal).HandlerFunc())

	return nil
}
