package service

import (
	"fmt"
	"strings"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter/distinct"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter/limit"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter/sift"
	sortTools "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/sort"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	// 随机字符串长度常量
	randomStringLength = 16
	// 过滤器名称前缀
	distinctFilterPrefix = "distinct_"
	limitFilterPrefix    = "limit_"
	siftFilterName       = "sift"
)

type FilterService struct {
	// 移除未使用的 Processor 字段，每次请求创建新实例
}

func (dp *FilterService) setDistinct(processor *filter.Processor, dis []*proto_process.Distinct) []string {
	names := make([]string, 0, len(dis))
	for i, d := range dis {
		name := fmt.Sprintf("%s%d", distinctFilterPrefix, i)
		processor.SetFilter(name, &distinct.Distinct{
			DistinctFields: d.Cond,
			ReserveFields:  d.Fields,
			KeySelector:    selector,
			ChooseItem:     chooseItem,
		})
		names = append(names, name)
	}
	return names
}

func (dp *FilterService) setSift(processor *filter.Processor, condition *proto_process.SiftCondition) string {
	siftFilter := &sift.Sift{Condition: condition}
	processor.SetFilter(siftFilterName, siftFilter)
	return siftFilterName
}

func (dp *FilterService) setLimit(processor *filter.Processor, cond []*proto_process.LimitField) []string {
	names := make([]string, 0, len(cond))
	for i, c := range cond {
		name := fmt.Sprintf("%s%d", limitFilterPrefix, i)
		processor.SetFilter(name, &limit.Limit{LimitField: c})
		names = append(names, name)
	}
	return names
}

// Process 数据处理流程
func (dp *FilterService) Process(span *pandora_span.Span, payload *proto_process.Payload) error {
	var filterNames []string

	processor := &filter.Processor{Filters: make(map[string]filter.Filter)}
	// 筛选
	if payload.Sift != nil {
		name := dp.setSift(processor, payload.Sift)
		filterNames = append(filterNames, name)
	}
	// 去重
	if payload.Distinct != nil {
		payload.Distinct = lo.Filter(payload.Distinct, func(item *proto_process.Distinct, _ int) bool { return item != nil })
		filterNames = append(filterNames, dp.setDistinct(processor, payload.Distinct)...)
	}
	// 限制
	if payload.Limit != nil {
		payload.Limit = lo.Filter(payload.Limit, func(item *proto_process.LimitField, _ int) bool { return item != nil })
		filterNames = append(filterNames, dp.setLimit(processor, payload.Limit)...)
	}

	// docs
	for i := range payload.Data {
		docs, err := processor.Apply(span, filterNames, payload.Data[i].Docs)
		if err != nil {
			return err
		}
		payload.Data[i].Docs = docs
	}

	// 处理完成之后再进行排序
	if payload.SortList != nil {
		err := sortTools.Sort(span, payload.SortList, payload)
		if err != nil {
			return err
		}
	}

	return nil
}

func selector(item bson.M, fields []string) (string, error) {
	values := make([]any, len(fields))
	for i, field := range fields {
		// 字段不存在，返回随机字符串,表示不去重
		if value, err := util.GetField(field, item); err != nil {
			values[i], _ = util.RandomString(randomStringLength)
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("distinct selector fails to get %s, err: %v", field, err))
		} else {
			// 进行特殊字符处理
			switch v := value.(type) {
			case string:
				value = distinct.Clean(v)
			default:
				fmt.Printf("the type is %T\n", value)
			}

			values[i] = value
		}
	}
	res := util.Concat(values)
	return res, nil
}

func chooseItem(item1, item2 bson.M, params []*proto_process.DistinctField) (bson.M, error) {
	compare := func(val1, val2 float64, operator string) int {
		switch operator {
		case "max":
			if val1 > val2 {
				return 1
			} else if val1 < val2 {
				return -1
			}
			// in操作,优先val小的
		case "min", "in":
			if val1 > val2 {
				return -1
			} else if val1 < val2 {
				return 1
			}
		}
		return 0
	}

	for _, param := range params {
		var val1, val2 float64
		field1, err := util.GetField(param.Field, item1)
		if err != nil {
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("distinct chooseItem fails to get %s, err: %v", param.Field, err))
			return item2, nil
		}
		field2, err := util.GetField(param.Field, item2)
		if err != nil {
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("distinct chooseItem fails to get %s, err: %v", param.Field, err))
			return item1, nil
		}

		switch param.Type {
		case "number":
			switch field1.(type) {
			case int:
				val1 = float64(field1.(int))
				val2 = float64(field2.(int))
			case int32:
				val1 = float64(field1.(int32))
				val2 = float64(field2.(int32))
			case int64:
				val1 = float64(field1.(int64))
				val2 = float64(field2.(int64))
			case float32:
				val1 = float64(field1.(float32))
				val2 = float64(field2.(float32))
			case float64:
				val1 = field1.(float64)
				val2 = field2.(float64)
			}
		case "string":
			val1 = float64(util.Contains2(strings.Split(param.Value, ","), fmt.Sprintf("%v", field1)))
			val2 = float64(util.Contains2(strings.Split(param.Value, ","), fmt.Sprintf("%v", field2)))
		default:
			continue
		}

		if result := compare(val1, val2, param.Operator); result != 0 {
			if result == 1 {
				return item1, nil
			}
			return item2, nil
		}
	}

	return item1, nil
}
