package sort

import (
	"fmt"
	"sort"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	// 小数据集阈值，使用插入排序
	smallDataThreshold = 50
)

func Sort(span *pandora_span.Span, sortList []*proto_process.SortField, payload *proto_process.Payload) error {
	if len(sortList) == 0 || len(payload.Data) == 0 {
		return nil
	}

	// 对每个 ReqData 中的 Docs 进行排序
	for _, reqData := range payload.Data {
		if len(reqData.Docs) <= 1 {
			continue
		}

		// 根据数据量选择排序算法
		if len(reqData.Docs) <= smallDataThreshold {
			insertionSort(reqData.Docs, sortList)
		} else {
			optimizedSort(reqData.Docs, sortList)
		}
	}
	return nil
}

// insertionSort 对小数据集使用插入排序
func insertionSort(docs []bson.M, sortList []*proto_process.SortField) {
	for i := 1; i < len(docs); i++ {
		key := docs[i]
		j := i - 1

		for j >= 0 && compareDocuments(docs[j], key, sortList) > 0 {
			docs[j+1] = docs[j]
			j--
		}
		docs[j+1] = key
	}
}

// optimizedSort 对大数据集使用优化的快速排序
func optimizedSort(docs []bson.M, sortList []*proto_process.SortField) {
	sort.Slice(docs, func(i, j int) bool {
		return compareDocuments(docs[i], docs[j], sortList) < 0
	})
}

// compareDocuments 比较两个文档，返回 -1, 0, 1
func compareDocuments(doc1, doc2 bson.M, sortList []*proto_process.SortField) int {
	for _, sortField := range sortList {
		// 获取两个文档中对应字段的值
		value1, err1 := util.GetField(sortField.Field, doc1)
		value2, err2 := util.GetField(sortField.Field, doc2)

		// 处理字段获取失败的情况
		if err1 != nil && err2 != nil {
			continue // 两个都取不到，比较下一个字段
		}
		if err1 != nil {
			return 1 // doc1 取不到值，排在后面
		}
		if err2 != nil {
			return -1 // doc2 取不到值，排在后面
		}

		// 比较值
		cmp := compareValues(value1, value2)
		if cmp != 0 {
			// 根据排序方向调整结果
			switch sortField.Sort {
			case model.SORT_TYPE_DESC:
				return -cmp
			case model.SORT_TYPE_ASC:
				return cmp
			}
		}
	}
	return 0 // 所有字段都相等
}

// compareValues 比较两个值，返回 -1, 0, 1
func compareValues(value1, value2 any) int {
	// 首先尝试数值比较
	if num1, ok1 := convertToFloat64(value1); ok1 {
		if num2, ok2 := convertToFloat64(value2); ok2 {
			if num1 < num2 {
				return -1
			} else if num1 > num2 {
				return 1
			}
			return 0
		}
	}

	// 字符串比较
	str1 := fmt.Sprintf("%v", value1)
	str2 := fmt.Sprintf("%v", value2)
	if str1 < str2 {
		return -1
	} else if str1 > str2 {
		return 1
	}
	return 0
}

// convertToFloat64 尝试将值转换为 float64
func convertToFloat64(value any) (float64, bool) {
	switch v := value.(type) {
	case int:
		return float64(v), true
	case int32:
		return float64(v), true
	case int64:
		return float64(v), true
	case float32:
		return float64(v), true
	case float64:
		return v, true
	default:
		return 0, false
	}
}
