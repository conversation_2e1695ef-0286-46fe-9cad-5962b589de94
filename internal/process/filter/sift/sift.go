package sift

import (
	"fmt"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"go.mongodb.org/mongo-driver/bson"
)

// Sift 根据 Condition 结构体进行筛选
type Sift struct {
	Condition *proto_process.SiftCondition
}

// 检查是否实现Filter接口
var _ filter.Filter = (*Sift)(nil)

func (s *Sift) Apply(input []bson.M) ([]bson.M, error) {
	if len(input) == 0 {
		return input, nil
	}

	result := make([]bson.M, 0, len(input))
	for _, item := range input {
		eval, err := s.evaluateCondition(item, s.Condition)
		if err != nil {
			// 记录错误但继续处理，避免单个项目错误影响整体处理
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("sift evaluate condition error: %v", err))
			continue
		}
		if eval {
			result = append(result, item)
		}
	}
	return result, nil
}

func (s *Sift) evaluateCondition(item bson.M, condition *proto_process.SiftCondition) (bool, error) {
	if condition == nil || condition.IsEmpty() {
		return true, nil
	}

	if len(condition.SubConditions) > 0 {
		matches := make([]bool, len(condition.SubConditions))
		hasError := false

		for i, subCondition := range condition.SubConditions {
			match, err := s.evaluateCondition(item, subCondition)
			if err != nil {
				hasError = true
				goboot.Logger().DefaultInstance().Error(fmt.Sprintf("sift evaluate sub-condition error: %v", err))
				matches[i] = false // 错误时默认为 false
			} else {
				matches[i] = match
			}
		}

		switch condition.Logic {
		case "AND":
			return all(matches), nil
		case "OR":
			return some(matches), nil
		default:
			if hasError {
				return false, fmt.Errorf("unknown logic operator: %s", condition.Logic)
			}
			return false, nil
		}
	}

	field, err := util.GetField(condition.Field, item)
	if err != nil {
		// 取不到值，默认为false，但不返回错误以避免中断整个处理流程
		goboot.Logger().DefaultInstance().Error(fmt.Sprintf("sift evaluate get field %s error: %v", condition.Field, err))
		return false, nil
	}

	result := util.EvaluateOperator(field, condition.FieldType, condition.Operator, condition.Origin)
	return result, nil
}

func all(matches []bool) bool {
	for _, match := range matches {
		if !match {
			return false
		}
	}
	return true
}

func some(matches []bool) bool {
	for _, match := range matches {
		if match {
			return true
		}
	}
	return false
}
