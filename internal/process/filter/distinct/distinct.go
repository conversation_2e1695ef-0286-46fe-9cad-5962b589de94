package distinct

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	"go.mongodb.org/mongo-driver/bson"
)

// Distinct 根据特定条件去重
type Distinct struct {
	KeySelector    func(item bson.M, fields []string) (string, error)
	ChooseItem     func(item1, item2 bson.M, params []*proto_process.DistinctField) (bson.M, error) // 选择保留的项
	ReserveFields  []*proto_process.DistinctField
	DistinctFields []string
}

// DistinctResult 去重结果，包含索引信息以优化性能
type DistinctResult struct {
	Items []bson.M
	Keys  []string
}

// 检查是否实现Filter接口
var _ filter.Filter = (*Distinct)(nil)

func (d *Distinct) Apply(input []bson.M) ([]bson.M, error) {
	if len(input) == 0 {
		return input, nil
	}

	// 对于小数据集，使用简单的线性查找
	if len(input) <= 10 {
		return d.applySimple(input)
	}

	// 对于大数据集，使用哈希表优化
	return d.applyOptimized(input)
}

// applySimple 对小数据集使用简单的线性查找
func (d *Distinct) applySimple(input []bson.M) ([]bson.M, error) {
	result := make([]bson.M, 0, len(input))

	for _, item := range input {
		key, err := d.KeySelector(item, d.DistinctFields)
		if err != nil {
			return nil, err
		}

		// 线性查找是否已存在
		found := false
		for i, existing := range result {
			existingKey, err := d.KeySelector(existing, d.DistinctFields)
			if err != nil {
				continue
			}

			if existingKey == key {
				// 选择保留的项
				choice, err := d.ChooseItem(existing, item, d.ReserveFields)
				if err != nil {
					return nil, err
				}
				result[i] = choice
				found = true
				break
			}
		}

		if !found {
			result = append(result, item)
		}
	}

	return result, nil
}

// applyOptimized 对大数据集使用哈希表优化
func (d *Distinct) applyOptimized(input []bson.M) ([]bson.M, error) {
	// 使用 string 作为 key 类型，避免 any 类型的内存问题
	seen := make(map[string]bson.M, len(input))
	order := make([]string, 0, len(input))

	for _, item := range input {
		// 取不到的字段,默认为空
		key, err := d.KeySelector(item, d.DistinctFields)
		if err != nil {
			return nil, err
		}

		// 保留条件取不到的那一项排除
		if existingItem, found := seen[key]; found {
			choice, err := d.ChooseItem(existingItem, item, d.ReserveFields)
			if err != nil {
				return nil, err
			}
			seen[key] = choice
		} else {
			seen[key] = item
			order = append(order, key)
		}
	}

	// 预分配结果切片容量
	result := make([]bson.M, 0, len(order))
	for _, key := range order {
		result = append(result, seen[key])
	}
	return result, nil
}
