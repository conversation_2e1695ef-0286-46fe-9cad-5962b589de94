package maputil

import (
	"encoding/json"
	"fmt"
	"testing"
)

type T1 struct {
	V1 string
	V2 string
}
type T2 struct {
	V1   string
	V2   string
	Name string
}

type T3 struct {
	K  []*T2
	K2 *T2
	K3 []*T2
}

func TestMap(t *testing.T) {
	m := map[string]any{
		"K": map[string]T1{
			"A": {V1: "1", V2: "2"},
			"B": {V1: "3", V2: "4"},
		},
		"K2": T1{
			V1: "a",
			V2: "b",
		},
		"K3": []T1{
			{V1: "a1", V2: "b1"},
			{V1: "c1", V2: "d1"},
		},
	}

	j, _ := json.Marshal(m)

	a := make(map[string]any)
	json.Unmarshal(j, &a)
	// tt := make([]*T2, 0)
	tt := T3{}
	d := MapTo(a, &tt)
	fmt.Println(d)
}
