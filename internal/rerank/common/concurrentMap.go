package common

import "sync"

type SafeMap[K comparable, V any] struct {
	mu sync.RWMutex
	m  map[K]V
}

func NewSafeMap[K comparable, V any]() *SafeMap[K, V] {
	return &SafeMap[K, V]{m: make(map[K]V)}
}

func (sm *SafeMap[K, V]) Set(Key K, Value V) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.m[Key] = Value
}

func (sm *SafeMap[K, V]) Get(Key K) (V, bool) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	value, ok := sm.m[Key]
	return value, ok
}

func (sm *SafeMap[K, V]) Delete(Key K) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	delete(sm.m, Key)
}
