package service

import (
	"context"
	"fmt"
	"sync"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common/maputil"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/rank"
	proto_rerank "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/rerank"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"golang.org/x/sync/errgroup"
)

type RerankService struct {
	Ctx           *pandora_context.PandoraContext
	medicalRerank *rank.MedicalRerank
	commonRerank  *rank.CommonRerank
	sportsRerank  *rank.SportsRerank
	newsRerank    *rank.NewsRerank
	Resp          *pandora_proto.PandoraResponseMessage[bean.RespPayLoad]
	Req           *pandora_proto.PandoraRequestMessage[bean.RequestPayLoad]
}

var rerankOnce sync.Once

func (s *RerankService) InitStrategy() {
	rerankOnce.Do(func() {
		s.medicalRerank = &rank.MedicalRerank{}
		s.commonRerank = &rank.CommonRerank{}
		s.sportsRerank = &rank.SportsRerank{}
	})
}

// 初始化Rerank服务
func (s *RerankService) Init() {
	s.InitStrategy()
}

func (s *RerankService) checkRequest(span *span.Span) *errtypes.SelfError {
	checkSpan := span.AddSpan("请求检查")
	defer checkSpan.Finish()
	// 检查请求
	if len(s.Req.Payload.Data) == 0 {
		return selferrors.RerankError_ReqDataEmptyError
	}
	if len(s.Req.Payload.Data[0].Query) == 0 {
		return selferrors.RerankError_ReqQueryEmptyError
	}

	for _, d := range s.Req.Payload.Data {
		idsMap := make(map[int64]int, 0)
		for _, doc := range d.Docs {
			value1 := common.Interface2I64((*doc)["id"])
			if _, ok := idsMap[value1]; ok {
				return selferrors.RerankError_ReqIDDuplicateError
			} else {
				idsMap[value1] = 1
			}
		}
	}
	return nil
}

func (s *RerankService) rank() {
	span := s.Ctx.RootSpan().AddSpan("执行重排服务")
	defer func() {
		span.TraceInfo("requestInfo", s.Req)
		span.TraceInfo("responseInfo", s.Resp)
		span.Finish()
	}()
	if err := s.checkRequest(span); err != nil {
		s.Resp.Header.TraceId = s.Req.Header.TraceId
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		return
	}

	if s.Req.Payload.Intent == consts.HOTSEARCH && s.Req.Payload.Model == consts.SPORTS {
		s.handleHotSearch()
		return
	}

	results, err := s.processRanking(span)
	if err != nil {

		s.Resp.Header.Code = selferrors.RerankError_UnknowError.Code()
		s.Resp.Header.Success = err.Error()
		return
	}
	s.Resp.Header.TraceId = s.Req.Header.TraceId
	s.Resp.Payload.Result = results
}

func (s *RerankService) handleHotSearch() {
	results := make([]*bean.QueryData, len(s.Req.Payload.Data))
	for i, d := range s.Req.Payload.Data {
		results[i] = &bean.QueryData{
			Query: d.Query,
			Type:  d.Type,
			Extra: d.Extra,
			Docs:  d.Docs,
		}
	}
	s.Resp.Header.Success = "hot_search不走重排，直接返回请求数据"
	s.Resp.Payload.Result = results
}

func (s *RerankService) processRanking(span *span.Span) ([]*bean.QueryData, error) {
	domainID := s.Req.Payload.Model
	resultMap := maputil.NewConcurrentMap[int, []*map[string]any](0)
	goboot.Logger().DefaultInstance().Info(fmt.Sprintf("重排领域id：%s\n", domainID))

	ctx, cancel := context.WithCancel(s.Ctx.GetContext())
	defer cancel()

	var eg errgroup.Group
	for i, data := range s.Req.Payload.Data {
		i, data := i, data // 创建局部变量以避免并发问题
		eg.Go(func() error {
			select {
			case <-ctx.Done():
				return nil // 如果上下文被取消，直接返回
			default:
				rerankRsts, err := s.performRanking(span, domainID, data)
				if err != nil {
					cancel() // 取消其他任务
					return fmt.Errorf("重排任务失败 (index=%d, query=%s): %s", i, data.Query, err.Error())
				}
				resultMap.Set(i, rerankRsts)
				return nil
			}
		})
	}

	if err := eg.Wait(); err != nil {
		goboot.Logger().DefaultInstance().Error(fmt.Sprintf("重排过程中发生错误: %v", err))
		return s.Req.Payload.Data, err
	}

	return s.collectResults(resultMap), nil
}

func (s *RerankService) performRanking(span *span.Span, domainID string, data *bean.QueryData) ([]*map[string]any, error) {
	switch domainID {
	case consts.MEDICAL, consts.V1120, consts.Medical_V1120, consts.HEALTH:
		return s.medicalRerank.Rank1120(span, s.Req, data)
	case consts.V1218, consts.Medical_V1218:
		return s.medicalRerank.Rank1218(span, s.Req, data)
	case consts.Medical_V0310:
		return s.medicalRerank.Rank0310(span, s.Req, data)
	case consts.SPORTS:
		return s.sportsRerank.Rank(span, s.Req, data)
	case consts.News_V20250421, consts.News_V20250522:
		return s.newsRerank.Rank20250421(span, s.Req, data)
	case consts.Medical_V0516:
		return s.medicalRerank.Rank0516(span, s.Req, data)
	default:
		return s.commonRerank.Rank(span, s.Req, data)
	}
}

func (s *RerankService) collectResults(resultMap *maputil.ConcurrentMap[int, []*map[string]any]) []*bean.QueryData {
	results := make([]*bean.QueryData, 0, len(s.Req.Payload.Data))
	for i, data := range s.Req.Payload.Data {
		if s.Req.Payload.Model == consts.News_V20250421 {
			if value, ok := resultMap.Get(i); ok {
				results = append(results, &bean.QueryData{
					Query:     data.Query,
					Type:      common.If(data.Type == "", "raw", data.Type),
					Extra:     common.If(len(data.Extra) == 0, []*map[string]any{}, data.Extra),
					Docs:      value,
					QKeywords: data.QKeywords,
				})
			}
		} else {
			if value, ok := resultMap.Get(i); ok {
				results = append(results, &bean.QueryData{
					Query:     data.Query,
					Type:      common.If(data.Type == "", "raw", data.Type),
					Extra:     common.If(len(data.Extra) == 0, []*map[string]any{}, data.Extra),
					Docs:      value,
					QKeywords: *proto_rerank.NewQueryKeyWords(),
				})
			}
		}

	}
	return results
}

func (s *RerankService) HttpHandler() {
	s.rank()
}
