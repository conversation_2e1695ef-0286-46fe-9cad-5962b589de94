package rank

import (
	"fmt"
	"math"
	"runtime"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"golang.org/x/sync/errgroup"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"github.com/mozillazg/go-pinyin"

	"sort"
)

// 比较两个中文词的拼音是否一致
func ComparePinyin(word1, word2 string) bool {
	args := pinyin.NewArgs()
	getPinyinStr := func(word string) string {
		var s []string
		for _, parts := range pinyin.Pinyin(word, args) {
			s = append(s, parts[0])
		}
		return strings.Join(s, "")
	}
	return getPinyinStr(word1) == getPinyinStr(word2)
}

func MergeWordsWithTag(words []string, scores []float64, tags []bool, threshold float64) ([]string, []float64, []bool) {
	var mergedWords []string
	var mergedScores []float64
	var mergedTags []bool

	var currentWords []string
	var currentScore float64
	var currentTag bool

	for i := 0; i < len(words); i++ {
		word := words[i]
		score := scores[i]
		tag := tags[i]

		if score > threshold && !tag { // 分数大于阈值且标签为 false
			currentWords = append(currentWords, word)
			currentScore += score
		} else {
			if len(currentWords) > 0 {
				// 如果有合并的单词，存储并重置
				mergedWords = append(mergedWords, strings.Join(currentWords, ""))
				mergedScores = append(mergedScores, currentScore)
				mergedTags = append(mergedTags, currentTag)
				currentWords = nil
				currentScore = 0
				currentTag = false
			}
			// 存储未合并的单词
			mergedWords = append(mergedWords, word)
			mergedScores = append(mergedScores, score)
			mergedTags = append(mergedTags, tag)
		}
	}

	// 合并最后剩下的单词
	if len(currentWords) > 0 {
		mergedWords = append(mergedWords, strings.Join(currentWords, ""))
		mergedScores = append(mergedScores, currentScore)
		mergedTags = append(mergedTags, currentTag)
	}

	return mergedWords, mergedScores, mergedTags
}

func MatchingIntersection(substring, str string) int {
	set := make(map[rune]bool)
	for _, char := range substring {
		set[char] = true
	}

	intersectionCount := 0
	for _, char := range str {
		if set[char] {
			intersectionCount++
		}
	}

	return intersectionCount
}

// 计算查询词和标题之间的匹配得分
func (s *MedicalRerank) WordsScore(query string, title string, queryKeysOrigin []string, queryValues []float64, titleKeys []string, titleValues []float64) (float64, error) {
	var wordsScore float64
	queryKeys := make([]string, len(queryKeysOrigin))
	copy(queryKeys, queryKeysOrigin)

	// 如果查询中含有数字，直接返回0得分
	if strings.ContainsAny(query, "**********") {
		wordsScore = 0
	} else {
		queryLen := len(queryKeys)
		titleLen := len(titleKeys)

		// 用来标记匹配的数组
		queryTag := make([]bool, queryLen)
		titleTag := make([]bool, titleLen)

		// 遍历标题和查询的关键词
		for i := 0; i < titleLen; i++ {
			for j := 0; j < queryLen; j++ {
				// 如果两个关键词都未匹配过且长度相同
				if !titleTag[i] && !queryTag[j] && len(titleKeys[i]) == len(queryKeys[j]) {
					// 直接匹配
					if titleKeys[i] == queryKeys[j] {
						// 直接匹配
						wordsScore += math.Min(titleValues[i], queryValues[j])
						titleTag[i] = true
						queryTag[j] = true

					} else if len(titleKeys[i]) > 3 && ComparePinyin(titleKeys[i], queryKeys[j]) {
						// 拼音匹配
						wordsScore += math.Min(titleValues[i], queryValues[j])
						titleTag[i] = true
						queryTag[j] = true
					}
				}
				if titleTag[i] {
					break
				}
			}
		}

		queryTagTemp := make([]bool, len(queryTag))
		copy(queryTagTemp, queryTag)

		titleTagTemp := make([]bool, len(titleTag))
		copy(titleTagTemp, titleTag)

		i := 0
		for i < titleLen {
			j := 0
			for j < queryLen {
				// 如果两个词都没有匹配
				if !titleTag[i] && !queryTag[j] {
					// 检查 titleKeys[i] 是否是 queryKeys[j] 的子串，反之亦然
					if strings.Contains(queryKeys[j], titleKeys[i]) || strings.Contains(titleKeys[i], queryKeys[j]) {
						// 计算子串比率
						substringRatio := float64(math.Min(float64(len(titleKeys[i])), float64(len(queryKeys[j])))) / float64(math.Max(float64(len(titleKeys[i])), float64(len(queryKeys[j]))))
						wordsScore += math.Min(titleValues[i], queryValues[j]) * substringRatio

						if len(titleKeys[i]) < len(queryKeys[j]) {
							// 子串匹配一：更新标记并减少查询词
							titleTag[i] = true
							queryTagTemp[j] = true
							queryKeys[j] = strings.Replace(queryKeys[j], titleKeys[i], "", 1)
							i = 0
						} else if len(titleKeys[i]) > len(queryKeys[j]) {
							// 子串匹配二：更新标记并减少标题词
							queryTag[j] = true
							titleTagTemp[i] = true
							titleKeys[i] = strings.Replace(titleKeys[i], queryKeys[j], "", 1)
							j = 0
						} else {
							// 子串匹配三：完全匹配
							titleTag[i] = true
							queryTag[j] = true
							break
						}
					} else {
						j++
					}
				} else {
					j++
				}
			}
			i++
		}
		for i := 0; i < len(queryTag); i++ {
			queryTag[i] = queryTag[i] || queryTagTemp[i]
		}

		for num := 0; num < 2; num++ {
			for i := 0; i < len(titleKeys); i++ {
				for j := 0; j < len(queryKeys); j++ {
					// 如果title和query的关键词还没有匹配
					if !titleTag[i] && !queryTag[j] {
						intersection := MatchingIntersection(queryKeys[j], titleKeys[i])
						// 判断交集的大小是否超过阈值
						if intersection >= int(math.Ceil(math.Min(float64(len(titleKeys[i])), float64(len(queryKeys[j])))*0.6/3)) {
							// 计算子串匹配的比例
							substringRatio := float64(intersection) / float64(math.Max(float64(len(queryKeys[j])), float64(len(titleKeys[i])))) * 3
							// 根据匹配得分计算最终得分
							wordsScore += math.Min(titleValues[i], queryValues[j]) * substringRatio
							// 标记已经匹配过的关键词
							queryTag[j] = true
							titleTag[i] = true
						}
					}
				}
			}
			if num == 0 {
				for i := 0; i < len(queryTag); i++ {
					queryTag[i] = queryTag[i] || queryTagTemp[i]
				}

				for i := 0; i < len(titleTag); i++ {
					titleTag[i] = titleTag[i] || titleTagTemp[i]
				}
				// 根据阈值合并查询和标题中的关键词和分数
				queryKeys, queryValues, queryTag = MergeWordsWithTag(queryKeys, queryValues, queryTag, 0.2)
				titleKeys, titleValues, titleTag = MergeWordsWithTag(titleKeys, titleValues, titleTag, 0.2)
			}
		}

		for i := 0; i < len(titleKeys); i++ {
			if len(titleKeys[i]) == 3 && !titleTag[i] {
				titleValues[i] /= 2
			}
		}

		for j := 0; j < len(queryKeys); j++ {
			if len(queryKeys[j]) == 3 && !queryTag[j] {
				queryValues[j] /= 2
			}
		}

		// 处理权重大于 0.05 且未匹配的词
		if wordsScore != 0 {
			queryIndexes := getIndexes(queryValues, 0.1)
			if len(queryIndexes) > 0 {
				for _, index := range queryIndexes {
					if !queryTag[index] && !strings.Contains(title, queryKeys[index]) {
						wordsScore -= queryValues[index]
						// ratio := float64(MatchingIntersection(title, queryKeys[index])) / float64(len(queryKeys[index])) * 3
						// wordsScore -= queryValues[index] * (1 - ratio)
					}
				}
			}

			titleIndexes := getIndexes(titleValues, 0.1)
			if len(titleIndexes) > 0 {
				for _, index := range titleIndexes {
					if !titleTag[index] && !strings.Contains(query, titleKeys[index]) {
						wordsScore -= titleValues[index]
					}
				}
			}
		}

		// 确保分数不会低于 0
		if wordsScore < 0 {
			wordsScore = 0
		}

		// 处理查询中最大权重的词如果未匹配
		queryMaxIndexes, err := getMaxWeightIndexes(queryValues)
		if err != nil {
			return wordsScore, err
		}
		for _, index := range queryMaxIndexes {
			if !queryTag[index] {
				if MatchingIntersection(queryKeys[index], title) <= len(queryKeys[index])/2 {
					wordsScore = 0
					if wordsScore > 0 {
						wordsScore = 0
					}
				}
			}
		}

		// 处理权重大于 0.1 的词
		queryIndexes := getIndexes(queryValues, 0.1)
		titleIndexes := getIndexes(titleValues, 0.1)

		// 构建临时的查询和标题字符串
		tempQuery := ""
		for _, index := range queryIndexes {
			tempQuery += queryKeys[index]
		}

		tempTitle := ""
		for _, index := range titleIndexes {
			tempTitle += titleKeys[index]
		}

		wordsScoreOrigin := wordsScore

		// 检查查询词是否匹配
		for _, index := range queryIndexes {
			if queryTag[index] { // Term with weight > 0.1 not matched
				wordsScore = wordsScoreOrigin
				break
			}
			if MatchingIntersection(tempQuery, title) <= len(tempQuery)/2 { // Not matched enough
				wordsScore = -1
			}
		}

		if wordsScore == -1 {
			for _, index := range titleIndexes {
				if titleTag[index] { // Term with weight > 0.1 not matched
					wordsScore = wordsScoreOrigin
					break
				}
				if MatchingIntersection(tempTitle, query) <= len(tempTitle)/2 { // Not matched enough
					wordsScore = -1
				}
			}
		}

	}

	return wordsScore, nil
}

func getIndexes(values []float64, threshold float64) []int {
	var indexes []int
	for i, value := range values {
		if value >= threshold {
			indexes = append(indexes, i)
		}
	}
	return indexes
}

func getMaxWeightIndexes(values []float64) ([]int, error) {
	var indexes []int
	maxValue, err := MaxValues(values)
	if err != nil {
		return indexes, err
	}
	for i, value := range values {
		if math.Abs(value-maxValue) <= maxValue*0.20 {
			indexes = append(indexes, i)
		}
	}
	return indexes, nil
}
func MaxValues(values []float64) (float64, error) {
	if len(values) > 0 {
		maxVal := values[0]
		for _, value := range values[1:] {
			if value > maxVal {
				maxVal = value
			}
		}
		return maxVal, nil
	} else {
		return 0, fmt.Errorf("slice is empty")
	}

}

// 站点策略得分
func UrlsScore(req *bean.Request, url, host string, keywordsScore float64, index int, tag bool, domains []string) float64 {
	var score float64
	isMedical := false
	urlTag := false

	// 精品站点判断
	if len(req.Payload.RankSites) != 0 {
		for _, site := range req.Payload.RankSites {
			if strings.Contains(url, site) && !urlTag {
				urlTag = true
			}
		}
	}
	if urlTag {
		isMedical = true
		if index <= 15 {
			if tag {
				if keywordsScore >= 0.8 && index <= 10 {
					score = keywordsScore * 0.3
				} else if keywordsScore > 0.2 {
					score = keywordsScore * 0.2
				} else {
					score = 0.010
				}
			} else {
				if keywordsScore > 0.2 {
					score = 0.008
				} else {
					score = 0.004
				}
			}
		} else {
			score = 0.004
		}
	} else {
		parts := strings.Split(url, "//")
		if len(parts) > 1 {
			domain := strings.Split(parts[1], "/")[0]
			for _, medicalDomain := range domains {
				if strings.Contains(domain, medicalDomain) {
					isMedical = true
					break
				}
			}
		}
	}

	if !isMedical {
		score = -1
	}

	// 讯飞医典
	if host == "mkb.iflyhealth.com" {
		if index <= 15 {
			if tag {
				if keywordsScore >= 0.8 && index <= 10 {
					score = 1.0
				} else if keywordsScore > 0.2 {
					score = keywordsScore * 0.3
				} else {
					score = 0.020
				}
			} else {
				if keywordsScore > 0.2 {
					score = 0.010
				} else {
					score = 0.008
				}
			}
		} else {
			score = 0.008
		}
	}

	return score
}

// 站点 level 权重得分
func levelsScore(level int) float64 {
	if weight, ok := consts.QualityScore[level]; ok {
		return weight * 0.02
	}
	return 0
}

func (s *MedicalRerank) Segcalc(req *bean.Request, span *span.Span, query string) (utils.SegRes, error) {
	opSpan := span.AddSpan("分词")
	defer opSpan.Finish()

	feature, err := global.Inst.Submit(func() (any, error) {
		m, err := utils.TitleSeg(req, span, query)
		return m, err
	})

	if err != nil {
		return utils.SegRes{}, err
	}

	r, err := feature.Result()
	if err != nil {
		return utils.SegRes{}, err
	}

	out := r.(utils.SegRes)
	return out, nil
}

func (s *MedicalRerank) DocProcess(req *bean.Request, span *span.Span, data *bean.QueryData, rerankList []*map[string]any) error {
	opSpan := span.AddSpan("计算词匹配得分")
	defer opSpan.Finish()

	var queryKeywordsKeys = []string{}
	var queryKeywordsValues = []float64{}
	var titleKeywordsKeysList = make([][]string, len(data.Docs))
	var titleKeywordsValuesList = make([][]float64, len(data.Docs))

	var g = &errgroup.Group{}
	g.SetLimit(runtime.NumCPU())

	g.Go(func() error {
		out, err := utils.QuerySeg(req, span, data.Query)
		if err != nil {
			return err
		}
		queryKeywordsKeys = out.Keys
		queryKeywordsValues = out.Value
		return nil
	})

	for index, doc := range data.Docs {
		g.Go(func() error {
			out, err := s.Segcalc(req, span, common.Interface2S((*doc)["title"]))
			if err != nil {
				return err
			}
			titleKeywordsKeysList[index] = out.Keys
			titleKeywordsValuesList[index] = out.Value
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil
	}

	// 计算wordScore
	for index, doc := range data.Docs {
		g.Go(func() error {
			// 计算词匹配得分
			pSpan := opSpan.AddSpan("计算词匹配得分")
			title := common.Interface2S((*doc)["title"])
			keywordsScore, err := s.WordsScore(data.Query, title, queryKeywordsKeys, queryKeywordsValues, titleKeywordsKeysList[index], titleKeywordsValuesList[index])
			if err != nil {
				return err
			}
			pSpan.Finish()
			// id2WordScore.Set(id, keywordsScore)
			rerankList[index] = common.DeepCopyMap(doc)
			(*rerankList[index])["words_score"] = keywordsScore
			(*rerankList[index])["keywords_score"] = keywordsScore
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil
	}
	return nil
}

func (s *MedicalRerank) Rank0310(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()

	// 中间过程明细，用于溯源
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)
	// rerankScore为最终结果
	var rerankScore = make([]*map[string]any, len(data.Docs))
	// rerankList为中间结果
	var rerankList = make([]*map[string]any, len(data.Docs))
	// doc处理计算词匹配得分,记录docid和wordScore的映射关系
	err := s.DocProcess(req, rankSpan, data, rerankList)
	if err != nil {
		return rerankList, err
	}

	// 词匹配得分排序
	sort.Slice(rerankList, func(i, j int) bool {
		if common.Interface2F64((*rerankList[i])["words_score"]) == common.Interface2F64((*rerankList[j])["words_score"]) {
			return common.Interface2F64((*rerankList[i])["_rank_score"]) > common.Interface2F64((*rerankList[j])["_rank_score"])
		}
		return common.Interface2F64((*rerankList[i])["words_score"]) > common.Interface2F64((*rerankList[j])["words_score"])
	})

	topFive := utils.PostProcess1(rankSpan, rerankList)
	utils.PostProcess2(rankSpan, rerankList, topFive)
	utils.PostProcess3(rankSpan, rerankList, topFive)

	// 最终得分计算
	var scoreMap = map[int64]float64{}
	for i := range rerankList {
		var ScoreDetail = &map[string]any{}
		id := common.Interface2I64((*rerankList[i])["id"])
		url := utils.ParseUrl(rerankList[i])
		urlScore := UrlsScore(
			req,
			url,
			common.Interface2S((*rerankList[i])["domain"]),
			common.Interface2F64((*rerankList[i])["keywords_score"]),
			int(common.Interface2I64((*rerankList[i])["index"])),
			common.Interface2Bool((*rerankList[i])["tag"]),
			config.MedicalDomais)
		var docLevel int

		// 质量等级获取和得分计算
		docLevel = int(common.Interface2I64((*rerankList[i])["q_user"]))
		if docLevel == 0 {
			docLevel = int(common.Interface2I64((*rerankList[i])["q_level"]))
		}
		levelScore := levelsScore(docLevel)
		(*rerankList[i])["_rerank_score"] = common.Interface2F64((*rerankList[i])["words_score"]) + (common.Interface2F64((*rerankList[i])["_rank_score"])-0.8)*10 + urlScore + levelScore
		scoreMap[id] = common.Interface2F64((*rerankList[i])["_rerank_score"])
		(*ScoreDetail)["url_score"] = urlScore
		(*ScoreDetail)["level_score"] = levelScore
		(*ScoreDetail)["words_score"] = common.Interface2F64((*rerankList[i])["words_score"])
		(*ScoreDetail)["keywords_score"] = common.Interface2F64((*rerankList[i])["keywords_score"])
		(*ScoreDetail)["_rank_score"] = (common.Interface2F64((*rerankList[i])["_rank_score"]) - 0.8) * 10
		id2ScoreDetail[id] = ScoreDetail
	}

	// 最终排序
	sort.Slice(rerankList, func(i, j int) bool {
		if common.Interface2F64((*rerankList[i])["_rerank_score"]) == common.Interface2F64((*rerankList[j])["_rerank_score"]) {
			return common.Interface2F64((*rerankList[i])["_rank_score"]) > common.Interface2F64((*rerankList[j])["_rank_score"])
		}
		return common.Interface2F64((*rerankList[i])["_rerank_score"]) > common.Interface2F64((*rerankList[j])["_rerank_score"])
	})

	rerankScore = make([]*map[string]any, 0)
	for _, doc := range data.Docs {
		score := common.Interface2F64((*doc)["_rank_score"])
		if score >= req.Payload.ScoreThreshold {

			id := common.Interface2I64((*doc)["id"])
			(*doc)["_rerank_score"] = scoreMap[id]
			rerankScore = append(rerankScore, doc)
		}
	}
	return utils.FinalResultsProcess(rankSpan, req, rerankScore, true)
}
