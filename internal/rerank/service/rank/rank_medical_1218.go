package rank

import (
	"fmt"

	"math"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"

	"github.com/dexyk/stringosim"
)

func (r *MedicalRerank) score_origin1218(score float64) float64 {
	if score == 1 {
		return 16
	}
	originScore := -math.Log((1 / score) - 1)
	return originScore
}

// MinValue 返回切片中的最小值
func MinValue(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0.0
	}
	minValue := numbers[0]
	for _, value := range numbers {
		if value < minValue {
			minValue = value
		}
	}
	return minValue
}

// MaxValue 返回切片中的最大值
func MaxValue(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0.0
	}
	maxValue := numbers[0]
	for _, value := range numbers {
		if value > maxValue {
			maxValue = value
		}
	}
	return maxValue
}

func (r *MedicalRerank) answerStage1218(data []*map[string]any) ([][]*map[string]any, bool, map[int64]float64, map[int64]float64) {

	scoreList := make([]float64, 0)
	var records = make([]*map[string]any, 0)
	for _, d := range data {
		rankScore := common.Interface2F64((*d)["_rank_score"])
		if rankScore >= 0 {
			rankScore = r.score_origin1218(rankScore)
			records = append(records, d)
			scoreList = append(scoreList, rankScore)
		}

	}

	// 创建一个与 scoreList 长度相同的索引切片
	indices := make([]int64, len(scoreList))
	for i := 0; i < len(scoreList); i++ {
		indices[i] = common.Interface2I64(i)
	}

	// 将索引和分数配对并生成字典
	scoreDict := make(map[int64]float64)
	for i, score := range scoreList {
		scoreDict[indices[i]] = score
	}

	// 根据分数对字典进行排序（从高到低）
	type kv struct {
		Key   int64
		Value float64
	}
	var sortedScoreDict []kv
	for k, v := range scoreDict {
		sortedScoreDict = append(sortedScoreDict, kv{k, v})
	}
	sort.Slice(sortedScoreDict, func(i, j int) bool {
		return sortedScoreDict[i].Value > sortedScoreDict[j].Value
	})

	// 初始化两个切片，一个用于存储键，一个用于存储值
	keys := make([]int64, 0, len(sortedScoreDict))
	values := make([]float64, 0, len(sortedScoreDict))

	// 遍历 map，将键和值分别追加到对应的切片中
	for _, kv := range sortedScoreDict {
		index := kv.Key
		score := kv.Value
		keys = append(keys, index)
		values = append(values, score)
		scoreDict[index] = score
	}

	score_mapping_normalized_dict := make(map[int64]float64)
	score_mapping_add_dict := make(map[int64]float64)
	mapping_add_tag := false

	intCount := make(map[int]int)
	for i := 0; i < len(values); i++ {
		integerPart := int(values[i])
		if count, exists := intCount[integerPart]; exists {
			intCount[integerPart] = count + 1
		} else {
			intCount[integerPart] = 1
		}

		if intCount[integerPart] > 10 {
			intCount[integerPart] = 0
			mapping_add_tag = true
			for j := 0; j < i; j++ {
				values[j] += 1
			}
		}
	}

	for i := 0; i < len(sortedScoreDict); i++ {
		sortedScoreDict[i].Key = keys[i]
		sortedScoreDict[i].Value = values[i]
	}

	groups := make(map[int][]float64)
	group_keys := make([]int, 0)
	for _, num := range values {
		integerPart := int(num)
		decimalPart := num - float64(integerPart)
		groups[integerPart] = append(groups[integerPart], decimalPart)
		if !common.Contains(group_keys, integerPart) {
			group_keys = append(group_keys, integerPart)
		}
	}

	normalizedNumbers := []float64{}
	for _, integerPart := range group_keys {
		decimals := groups[integerPart]
		minDecimal := MinValue(decimals)
		maxDecimal := MaxValue(decimals)

		for _, decimal := range decimals {

			var normalizedDecimal float64
			if maxDecimal != minDecimal {
				normalizedDecimal = 0.01 + (decimal-minDecimal)/(maxDecimal-minDecimal)*(0.99-0.01)
			} else {
				normalizedDecimal = 0
			}
			normalizedNumbers = append(normalizedNumbers, float64(integerPart)+normalizedDecimal)
		}
	}

	// Update values with normalized numbers
	for i := 0; i < len(normalizedNumbers); i++ {
		values[i] = normalizedNumbers[i]
	}

	/*   score_mapping_normalized_dict    */
	// Create a new dictionary with sorted keys

	normalizedDict := make(map[int64]float64)
	for i := 0; i < len(keys); i++ {
		normalizedDict[keys[i]] = values[i]
	}

	var normalizedSortedDict []kv
	for k, v := range normalizedDict {
		normalizedSortedDict = append(normalizedSortedDict, kv{k, v})
	}
	sort.Slice(normalizedSortedDict, func(i, j int) bool {
		return normalizedSortedDict[i].Key < normalizedSortedDict[j].Key
	})

	for i, val := range normalizedSortedDict {
		values[i] = val.Value
	}

	for i, d := range records {
		score_mapping_normalized_dict[common.Interface2I64((*d)["id"])] = values[i]
	}

	/*   score_mapping_add_dict    */
	sort.Slice(sortedScoreDict, func(i, j int) bool {
		return sortedScoreDict[i].Key < sortedScoreDict[j].Key
	})

	for i, val := range sortedScoreDict {
		values[i] = val.Value
	}

	for i, d := range records {
		score_mapping_add_dict[common.Interface2I64((*d)["id"])] = values[i]
	}

	// sort.Slice(values, func(i, j int) bool {
	// 	return values[i] > values[j]
	// })
	max := MaxValue(values)

	// Create truncate thresholds and cluster list
	truncatThresholds := make([]int, int(max)+1)
	for i := range truncatThresholds {
		truncatThresholds[i] = i
	}

	clusterList := make([][]*map[string]any, len(truncatThresholds))
	for index, score := range scoreDict {
		if int(score) >= 0 {
			clusterList[int(score)] = append(clusterList[int(score)], data[index])
		} else {
			if len(truncatThresholds)+int(score) < 0 {
				continue
			}
			clusterList[len(truncatThresholds)+int(score)] = append(clusterList[len(truncatThresholds)+int(score)], data[index])
		}
	}

	// Create records box
	var recordsBox [][]*map[string]any
	for _, itemList := range clusterList {
		if len(itemList) > 0 {
			recordsBox = append(recordsBox, itemList)
		}
	}
	return recordsBox, mapping_add_tag, score_mapping_normalized_dict, score_mapping_add_dict
}

func arange1218(start, stop, step float64) []float64 {
	var s []float64
	if step == 0 {
		s = append(s, start)

	} else {
		for i := start; i < stop; i += step {
			s = append(s, i)
		}
	}
	return s
}

func keywordFeat(queryKeywords, contentKeywords []string, content string) (float64, float64, float64, float64) {
	var keywordsLength int
	qkLen := len(queryKeywords)
	ckLen := len(contentKeywords)
	qkSet := make(map[string]float64, qkLen)
	ckSet := make(map[string]float64, ckLen)

	for _, v := range queryKeywords {
		qkSet[v] = 1 / float64(qkLen)
	}

	for _, v := range contentKeywords {
		ckSet[v] = 1 / float64(ckLen)
	}

	queryFeat := make([]float64, 10)
	contentFeat := make([]float64, 10)

	// 找出交集
	var intersection []string
	for k := range qkSet {
		if _, exists := ckSet[k]; exists {
			intersection = append(intersection, k)
		}
	}

	// 取前10个元素
	if len(intersection) > 10 {
		intersection = intersection[:10]
	}

	var keywordProp float64
	if len(intersection) == 0 {
		keywordProp = 0
	} else {
		keywordProp = float64(len(intersection)) / float64(qkLen)
	}

	for _, v := range queryKeywords {
		if strings.Contains(content, v) {
			keywordsLength++
		}
	}

	intersectionMap := make([]*bean.Keyword, 0)
	if len(intersection) > 0 {
		for _, v := range intersection {
			intersectionMap = append(intersectionMap, &bean.Keyword{
				Key:    v,
				Weight: qkSet[v],
			})
		}

		sort.Slice(intersectionMap, func(i, j int) bool {
			return intersectionMap[i].Weight > intersectionMap[j].Weight
		})

		intersection = common.Map(intersectionMap, func(index int, item *bean.Keyword) string {
			return item.Key
		})

		for idx, val := range intersection {
			queryFeat[idx] = qkSet[val]
			contentFeat[idx] = ckSet[val]
		}
	}

	var keywordLen float64
	if qkLen == 0 {
		keywordLen = 0
	} else {
		keywordLen = float64(keywordsLength / qkLen)
	}

	return common.Sum(queryFeat), common.Sum(contentFeat), keywordProp, keywordLen
}

func contains(content, keyword string) bool {
	return len(keyword) > 0 && len(content) > 0 && len(content) >= len(keyword) && content[:len(keyword)] == keyword
}

func sum(slice []float64) float64 {
	total := 0.0
	for _, value := range slice {
		total += value
	}
	return total
}

func keywordFeat2(queryKeywords, contentKeywords []string, content string) (float64, float64, float64, float64) {
	// Initialize weights for query and content keywords
	queryWeights := make(map[string]float64)
	contentWeights := make(map[string]float64)
	for _, keyword := range queryKeywords {
		queryWeights[keyword] = 1 / float64(len(queryKeywords))
	}
	for _, keyword := range contentKeywords {
		contentWeights[keyword] = 1 / float64(len(contentKeywords))
	}

	// Initialize feature slices
	queryFeat := make([]float64, 10)
	contentFeat := make([]float64, 10)

	// Find intersection of query and content keywords
	intersection := make(map[string]bool)
	for _, keyword := range queryKeywords {
		if _, exists := contentWeights[keyword]; exists {
			intersection[keyword] = true
		}
	}

	// Convert map keys to slice and sort by weight
	var sortedIntersection []string
	for keyword := range intersection {
		sortedIntersection = append(sortedIntersection, keyword)
	}
	sort.Slice(sortedIntersection, func(i, j int) bool {
		return queryWeights[sortedIntersection[i]] > queryWeights[sortedIntersection[j]]
	})

	// Calculate keyword proportion and length in content
	var keywordProp = 0.0
	if len(queryKeywords) == 0 {
		keywordProp = 0
	} else {
		keywordProp = float64(len(sortedIntersection)) / float64(len(queryKeywords))
	}

	keywordLength := 0.0
	for _, keyword := range queryKeywords {
		if contains(content, keyword) {
			keywordLength++
		}
	}
	if len(queryKeywords) == 0 {
		keywordLength = 0
	} else {
		keywordLength /= float64(len(queryKeywords))
	}

	// Populate features based on sorted intersection
	for i, keyword := range sortedIntersection {
		if i >= 10 {
			break
		}
		queryFeat[i] = queryWeights[keyword]
		contentFeat[i] = contentWeights[keyword]
	}

	// Return the sum of features and calculated values
	return sum(queryFeat), sum(contentFeat), keywordProp, keywordLength
}

func calcRerankScore1218(id int64, query string, timeDiff int64, summaryLen int, score, lcs, queryScore, summaryScore, keywordProp, keywordsLen, summaryKeywords,
	qualityScore, jaccardDist, levenshteinDist, suppressWeight float64, authority, pr, freq, top, strongTimeliness, domainFreq, realmFreq int32) (float64, map[string]any) {
	summaryScore = math.Tanh(float64(summaryLen)/10000) * 0.3
	lcsScore := float64(lcs) * 0.01 * suppressWeight
	jaccardScore := jaccardDist * 0.01 * suppressWeight
	keywordPropScore := keywordProp * 0.005
	keywordsLenScore := keywordsLen * 0.005
	keywordsSummaryScore := summaryKeywords * 0.01
	// authorityScore := math.Tanh(float64(authority)) * 0.01
	levenshteinDistScore := common.Min(levenshteinDist, float64(utf8.RuneCountInString(query))) * -0.0003 / suppressWeight
	// domainFreqScore := math.Tanh(0.00000001*float64(domainFreq)) * 0.1

	var rankScore float64 = 0
	var rerankScore float64 = 0
	scoreDetail := make(map[string]any, 0)
	scoreDetail["summary_score"] = summaryScore // 0.014538602311465032
	rankScore += summaryScore
	scoreDetail["lcs_score"] = lcsScore // 0.01
	rankScore += lcsScore

	scoreDetail["jaccard_score"] = jaccardScore // 0.002
	rankScore += jaccardScore

	scoreDetail["keyword_prop_score"] = keywordPropScore //0.005
	rankScore += keywordPropScore

	scoreDetail["keywords_length_score"] = keywordsLenScore //0.005
	rankScore += keywordsLenScore

	scoreDetail["keywords_in_summary_score"] = keywordsSummaryScore //0.01
	rankScore += keywordsSummaryScore

	// scoreDetail["authority_score"] = authorityScore //0
	// rankScore += authorityScore

	scoreDetail["levenshtein_dist_score"] = levenshteinDistScore //-0.009000000000000001
	rankScore += levenshteinDistScore

	// scoreDetail["domain_freq_score"] = domainFreqScore //0
	// rankScore += domainFreqScore

	rerankScore = rankScore*5 + score*18
	// fmt.Printf("id:%v, 分数1:%v, 分数2:%v, 分数3:%v\n", id, rankScore, score, rerankScore)
	scoreDetail["score_detail_values"] = rankScore * 5
	scoreDetail["rank_score_factor_score"] = score * 18

	scoreDetail["levenshtein_dist"] = levenshteinDist
	scoreDetail["summary_length"] = summaryLen
	scoreDetail["lcs"] = lcs
	scoreDetail["jaccard_dist"] = jaccardDist
	scoreDetail["keyword_prop"] = keywordProp
	scoreDetail["keyword_length"] = keywordsLen

	return rerankScore, scoreDetail
}

func (r *MedicalRerank) rerankProcess1218(req *bean.Request, data *map[string]any, domainId string, query, category string, currentTime int64, score float64, mkb_tag bool) (float64, map[string]any) {
	var Summary = common.Interface2S((*data)["summary"])
	var Content = common.Interface2S((*data)["content"])
	if Summary == "" {
		Summary = common.TruncateString(Content, 512)
	}

	query = common.RemovePunctuation(query)

	summary := common.If(Summary == "", "", strings.ToLower(strings.TrimSpace(Summary)))
	ts := common.Interface2I64((*data)["post_ts"])

	timeDiff := currentTime - ts
	title := common.Interface2S((*data)["title"])
	// title = strings.ToLower(title)
	title = common.RemovePunctuation(title)

	summaryLen := utf8.RuneCountInString(summary)

	// 使用下划线分割标题字符串
	titleSegs := strings.Split(title, "_")

	// 根据下划线的数量进行不同的处理
	if len(titleSegs) >= 3 {
		// 保留前部分的段，去掉最后两个段
		title = strings.Join(titleSegs[:len(titleSegs)-2], "_")
	} else if len(titleSegs) == 2 {
		// 只保留第一个段
		title = titleSegs[0]
	}
	// id := common.Interface2I64((*data)["id"])

	queryKeywords := common.SetDifference(config.Seg.CutStop(query), config.StopWords)
	titleKeywords := common.SetDifference(config.Seg.CutStop(title), config.StopWords)
	// fmt.Printf("id:%v -- query:%v -- title:%v -- titleKeywords:%v\n", id, query, title, config.Seg.CutStop(title))
	rerankKeywords := make([]string, 0)

	for _, item := range titleKeywords {
		rerankKeywords = append(rerankKeywords, item)
	}
	// fmt.Println(rerankKeywords)
	(*data)["_rerank_keywords"] = strings.Join(rerankKeywords, " ")
	strongTimeliness := 1

	var wg sync.WaitGroup
	var levenshteinDist int
	var lcs, jaccardDist float64

	wg.Add(3)
	go func() {
		defer wg.Done()

		lcs = float64(stringosim.LCS([]rune(query), []rune(title))) / float64(utf8.RuneCountInString(query))
		// data.Lcs = lcs
	}()

	go func() {
		defer wg.Done()

		//jaccardDist = stringosim.Jaccard([]rune(query), []rune(title), []int{1})
		jaccardDist = common.JaccardDistance2(query, title)
		// slog.DebugF("id:%v,query:%s,title:%s,jaccard:%v\n", common.Interface2I64((*data)["id"]), query, title, jaccardDist)
	}()

	go func() {
		defer wg.Done()

		levenshteinDist = stringosim.Levenshtein([]rune(query), []rune(title))
	}()

	wg.Wait()

	realmFreq := 0
	freq := 1

	// pr := data.Pr

	queryScore, summaryScore, keywordProp, keywordsLen := keywordFeat2(queryKeywords, titleKeywords, title)

	summaryKeywords := 0
	for _, item := range queryKeywords {
		if strings.Contains(summary, item) {
			summaryKeywords++
		}
	}
	if len(queryKeywords) == 0 {
		summaryKeywords = 0
	} else {
		summaryKeywords = summaryKeywords / len(queryKeywords)
	}

	suppressWeight := 1.0
	if float64(len(title)) < float64(len(strings.Join(queryKeywords, "")))*0.4 {
		suppressWeight = 0.6
	}
	qualityScore := 0

	var domainFreq int32 = 0
	var authority int32 = 0
	var pr int32 = 0
	var top int32 = 0

	rerankScore, scoreDetail := calcRerankScore(common.Interface2I64((*data)["id"]), query, timeDiff, summaryLen, float64(score), lcs, float64(queryScore), float64(summaryScore), float64(keywordProp), float64(keywordsLen), float64(summaryKeywords),
		float64(qualityScore), jaccardDist, float64(levenshteinDist), suppressWeight, authority, pr, int32(freq), top, int32(strongTimeliness), int32(domainFreq), int32(realmFreq))

	// 去除视频类新闻
	minusVideo := true

	for key := range consts.VideoKeywords {
		if strings.Contains(query, key) {
			minusVideo = false
			break
		}
	}

	if minusVideo {
		for key := range consts.VideoKeywords {
			if strings.Contains(summary, key) {
				rerankScore -= 2
			}
		}
	}
	scoreDetail["minusVideo"] = minusVideo

	scoreDetail["score_without_level_score"] = rerankScore

	var docLevel int = 1
	var levelWeight float64

	// docLevel = int(common.Interface2I64((*data)["q_level"]))
	if t, ok := (*data)["levels"].(map[string]any); ok {
		// fmt.Println(t)
		for key, value := range t {
			if common.Interface2S(key) == config.G_ParamModule.DefaultInstance().HealthLevel {
				v := common.Interface2I64(value)
				docLevel = int(v)
			}

		}
	} else {
		docLevel = 1
	}
	// fmt.Println(docLevel)

	// slog.DebugF("质量得分:%v", docLevel)
	// var AuthorScore int32 = 0
	if _, ok := consts.QualityScore[docLevel]; !ok {
		levelWeight = 0
	} else {
		levelWeight = consts.QualityScore[docLevel]
	}
	levelScore := levelWeight * 0.7

	scoreDetail["level_socre"] = levelScore

	rerankScore += levelScore

	scoreDetail["score_with_level"] = rerankScore

	var url string
	protocal, ok := (*data)["protocol"].(string)
	if !ok {
		protocal = ""
	}
	domain, ok := (*data)["domain"].(string)
	if !ok {
		domain = ""
	}
	path, ok := (*data)["path"].(string)
	if !ok {
		path = ""
	}
	url = common.Interface2S((*data)["url"])
	if len(url) == 0 {
		if strings.Contains(protocal, "://") {
			url = fmt.Sprintf("%s%s%s", protocal, domain, path)
		} else {
			url = fmt.Sprintf("%s://%s%s", protocal, domain, path)
		}
	}
	// 判断是不是讯飞医典的数据
	if domain == "mkb.iflyhealth.com" && !mkb_tag {
		scoreDetail["mkb_tag_score"] = 0.5
		rerankScore += 0.5
		mkb_tag = true
	}
	scoreDetail["mkb_tag_score"] = 0

	urlTag := false
	indexCode := common.Interface2S((*data)["_indexCode"])
	if len(req.Payload.RankSites) != 0 {
		for _, site := range req.Payload.RankSites {
			if strings.Contains(url, site) && !urlTag {
				urlTag = true
			}
		}
	}
	if len(req.Payload.RankCollections) == 0 {
		if value, ok := config.RankCollectionsMap[domainId]; ok {
			if indexCode != "" && common.Contains(value, indexCode) && !urlTag {
				urlTag = true
			}
		}

	} else {
		if indexCode != "" && common.Contains(req.Payload.RankCollections, indexCode) {
			urlTag = true
		}
	}

	isMedical := false
	//  判断是否为医疗领域站点
	if strings.Contains(url, "m.baidu.com/bh/m/detail/ar") || strings.Contains(url, "health.baidu.com/m/detail/ar") {
		isMedical = true
		scoreDetail["baike_score"] = 0.8
		// urlTag = true
	} else {
		if strings.Contains(url, "//") {
			domain := strings.Split(strings.Split(url, "//")[1], "/")[0]
			for _, medicalDomain := range config.MedicalDomais {
				if strings.Contains(domain, medicalDomain) {
					isMedical = true
				}
			}
		}
	}

	// 特定来源的内容加权
	if urlTag {
		rerankScore += 0.8
	}
	scoreDetail["urlTag"] = urlTag
	if !isMedical {
		rerankScore -= 1
	}
	scoreDetail["isMedical"] = isMedical
	scoreDetail["score_with_medical"] = rerankScore
	return rerankScore, scoreDetail
}

func (r *MedicalRerank) Rank1218(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()
	currentTime := time.Now().Unix() * 1000
	var category string
	var rerankScore = make([]*map[string]any, 0)
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	timeStrength := 1
	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)

	// 判断是否是强时间性query
	// 转换为小写并去除首尾空格
	query := strings.ToLower(strings.TrimSpace(data.Query))
	// 定义正则表达式模式
	pattern := regexp.MustCompile(consts.TimeStrengthReg)
	if len(pattern.FindAllString(query, -1)) > 0 {
		timeStrength = 3
	}

	var addScore float64 = 4
	mkb_tag := false

	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered from panic:", r)
		}
	}()
	docs := data.Docs

	// // 分段，将结果按照精排模型分数按分段区分
	// sort.Slice(docs, func(i, j int) bool {
	// 	score1 := common.Interface2F64((*docs[i])["_rank_score"])

	// 	score2 := common.Interface2F64((*docs[j])["_rank_score"])

	// 	return score1 > score2
	// })
	var tmpData [][]*map[string]any

	// tmpData = r.answerStage2(docs, req.PayLoad.ScoreThreshold)
	tmpData, _, score_mapping_normalized_dict, score_mapping_add_dict := r.answerStage1218(docs)
	add_score := 3.5
	for i := len(tmpData) - 1; i >= 0; i-- {
		datas := tmpData[i]
		add_score_ := add_score - 0.4
		var tsList []int

		var rankScoreList []float64
		dataCount := len(datas)
		for _, v := range datas {
			postTs, ok := (*v)["post_ts"].(int64)
			if !ok {
				tsList = append(tsList, -int(0))
			} else {
				tsList = append(tsList, -int(postTs))
			}
			tsList = append(tsList, -int(postTs))
			score := common.Interface2F64((*v)["_rank_score"])
			rankScoreList = append(rankScoreList, float64(score))
		}

		// 统计时间重复率
		sort.Slice(tsList, func(i, j int) bool {
			return tsList[i] < tsList[j]
		})

		tsUnique := common.RemoveDuplicates(tsList)

		timeScore := common.Map(tsUnique, func(index int, item int) float64 {
			return 1 - math.Tanh(0.2*float64(index))
		})

		timeScoreDict := make(map[int]float64, dataCount)

		for i := 0; i < len(tsUnique); i++ {
			timeScoreDict[tsUnique[i]] = timeScore[i]
		}

		for _, d := range datas {
			id := common.Interface2I64((*d)["id"])
			_score := common.Interface2F64((*d)["_rank_score"])

			score, scoreDetail := r.rerankProcess1218(req, d, "", query, category, currentTime, _score, mkb_tag)
			scoreDetail["score_without_ts"] = score

			id2ScoreDetail[id] = &scoreDetail
			postTs := common.Interface2I64((*d)["post_ts"])

			tsScore := timeScoreDict[-int(postTs)] * 0.0001 * float64(timeStrength)
			scoreDetail["ts"] = tsScore

			scoreDetail["add_score"] = float64(addScore)
			// scoreDetail["add_factor"] = float64(conf.AddDactor)
			scoreDetail["time_strength"] = float64(timeStrength)
			scoreDetail["time_score_dict"] = timeScoreDict
			score_mapping_normalized := score_mapping_normalized_dict[id]
			add_score_order := add_score_ + 1*(score_mapping_normalized-float64(int(score_mapping_normalized)))/2
			scoreDetail["add_score_"] = add_score_
			scoreDetail["score_mapping_normalized"] = score_mapping_normalized
			scoreDetail["add_score_order"] = add_score_order
			scoreDetail["score_mapping_add"] = score_mapping_add_dict[id]
			score += tsScore + float64(add_score_order)
			// fmt.Printf("id:%v, add_score_:%v\n", id, add_score_)
			scoreDetail["rerank_score"] = score

			(*d)["_rerank_score"] = score
			rerankScore = append(rerankScore, d)

		}
		add_score = add_score - 0.7
	}

	return utils.FinalResultsProcess(rankSpan, req, rerankScore, true)
}
