package rank

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"

func CalcScore(authorScore float64, level int) float64 {
	var levelScore float64
	if level != 1 && level != 2 && level != 3 && level != 4 {
		levelScore = consts.QualityScore[level]
	} else {
		authorLevel := getAuthorLevel(authorScore)
		levelScore = articleScore(level, authorLevel) / 4
	}

	return levelScore
}

func getAuthorLevel(score float64) int {
	// 作者level映射
	level := 1
	if score <= 1 && score >= 0.9 {
		level = 4
	} else if score >= 0.7 && score < 0.9 {
		level = 3
	} else if score >= 0.4 && score < 0.7 {
		level = 2
	}

	return level
}

func articleScore(docLevel int, authorLevel int) float64 {
	var docScore, levelScore float64

	score, ok := consts.LevelScoreDict[docLevel]
	if ok {
		docScore = score
	} else {
		docScore = 0
	}

	switch authorLevel {
	case 4:
		levelScore = docScore + 2
	case 3:
		levelScore = docScore + 1.5
	case 2:
		levelScore = docScore + 0.8
	default:
		levelScore = docScore
	}

	return levelScore

}

func CalMedicalScore(authorScore float64, level int) float64 {
	if level == 1 && authorScore > 0 {
		authorLevel := getAuthorLevel(authorScore)
		levelScore := articleScore(level, authorLevel) / 4
		return levelScore * 1
	}
	return consts.QualityScore[level] * 0.9 * 1
}
