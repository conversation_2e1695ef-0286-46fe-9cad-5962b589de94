package utils

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

func PostProcess1(span *span.Span, rerankList []*map[string]interface{}) int {
	opSpan := span.AddSpan("后处理一")
	defer opSpan.Finish()
	topFive := len(rerankList)
	if topFive > 5 {
		topFive = 5
	}
	var num int
	if len(rerankList) > 20 {
		num = 10
	} else {
		num = 5
	}
	if topFive-1 >= 0 && topFive-1 < len(rerankList) {
		if common.Interface2F64((*rerankList[topFive-1])["words_score"]) == 0 {
			topTag := true
			for i := 0; i < topFive; i++ {
				if common.Interface2F64((*rerankList[i])["score"]) > 0 && int(common.Interface2I64((*rerankList[i])["index"])) < num {
					topTag = false
				}
			}

			if topTag {
				for i := range rerankList {
					if common.Interface2F64((*rerankList[i])["score"]) != -1 {
						(*rerankList[i])["words_score"] = 0.0
					}
				}
			}

			for i := range rerankList {
				(*rerankList[i])["tag"] = false
			}
		}
	}

	return topFive
}

func PostProcess2(span *span.Span, rerankList []*map[string]interface{}, topFive int) {
	// 后处理二
	opSpan := span.AddSpan("后处理二")
	defer opSpan.Finish()
	difference := 0
	for i := 0; i < topFive; i++ {
		if common.Interface2I64((*rerankList[i])["index"]) > 5 {
			difference++
		}
	}
	if difference == 5 {
		for i := range rerankList {
			if common.Interface2F64((*rerankList[i])["score"]) < 0.8 {
				if common.Interface2F64((*rerankList[i])["score"]) != -1 {
					(*rerankList[i])["words_score"] = 0.0
				}
				(*rerankList[i])["tag"] = false
			}
		}
	}
	opSpan.Finish()

}

func PostProcess3(span *span.Span, rerankList []*map[string]interface{}, topFive int) {
	// 后处理三
	opSpan := span.AddSpan("后处理三")
	defer opSpan.Finish()
	oneTag := true
	topTen := len(rerankList)
	if topTen > 10 {
		topTen = 10
	}
	for i := 0; i < topTen; i++ {
		if int(common.Interface2I64((*rerankList[i])["index"])) == 1 {
			oneTag = false
		}
	}
	if oneTag && topFive < len(rerankList) && common.Interface2F64((*rerankList[topFive])["words_score"]) < 0.8 {
		for i := range rerankList {
			if common.Interface2F64((*rerankList[i])["words_score"]) < 0.8 {
				if common.Interface2F64((*rerankList[i])["words_score"]) != -1 {
					(*rerankList[i])["words_score"] = 0.0
				}
				(*rerankList[i])["tag"] = false
			}
		}
	}
}
