// package main
package utils

import (
	// "fmt"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
)

// var blockDict = []string{
// 	"科技",
// 	"事件", "实事", "消息", "动态",
// 	"热门", "热点", "最新", "最热",
// 	"新闻", "要闻", "趣闻", "话题", "问题", "内容",
// 	"分析", "报道", "推荐",
// }
// var prefixDict = []string{
// 	"中国",
// }
// var suffixDict = []string{
// 	"省", "市", "区", "县", "乡", "镇", "街道",
// 	"公园", "森林公园", "景区",
// 	"年",
// }
// var synonymsDict = [][]string{
// 	{"人工智能", "AI"},
// }

// removeWords 移除屏蔽词
func RemoveWords(wordList []string) []string {
	var result []string
	for _, str := range wordList {
		for _, word := range config.G_ResModule.DefaultInstance().BlockDict {
			str = strings.ReplaceAll(str, word, "")
		}
		if strings.TrimSpace(str) != "" {
			result = append(result, str)
		}
	}
	return result
}

// removePrefix 移除前缀
func RemovePrefix(wordList []string) []string {
	var result []string
	for _, word := range wordList {
		for _, prefix := range config.G_ResModule.DefaultInstance().PrefixDict {
			if strings.HasPrefix(word, prefix) {
				word = strings.TrimPrefix(word, prefix)
				break
			}
		}
		result = append(result, word)
	}
	return result
}

// removeSuffix 移除后缀
func RemoveSuffix(wordList []string) []string {
	var result []string
	for _, word := range wordList {
		for _, suffix := range config.G_ResModule.DefaultInstance().SuffixDict {
			if strings.HasSuffix(word, suffix) {
				word = strings.TrimSuffix(word, suffix)
				break
			}
		}
		result = append(result, word)
	}
	return result
}

// removeAffix 移除前后缀
func RemoveAffix(wordList []string) []string {
	wordList = RemovePrefix(wordList)
	wordList = RemoveSuffix(wordList)
	return wordList
}

// synonymMatch 同义词匹配
func SynonymMatch(word string, title string) bool {
	for _, synGroup := range config.G_ResModule.DefaultInstance().SynonymsDict {
		isWordInGroup := false
		for _, syn := range synGroup {
			if syn == word {
				isWordInGroup = true
				break
			}
		}
		if isWordInGroup {
			for _, syn := range synGroup {
				if syn == word {
					continue
				}
				if strings.Contains(title, syn) {
					return true
				}
			}
		}
	}
	return false
}

// func main() {
// 	wordList := []string{"中国科技新闻", "北京事件", "AI", "要闻"}
// 	title := "中国科技人工智能进展"
//     // 测试 removeWords
//     fmt.Println("移除屏蔽词后:", removeWords(wordList))
//     // 测试 removePrefix
//     fmt.Println("移除前缀后:", removePrefix(wordList))
//     // 测试 removeSuffix
//     fmt.Println("移除后缀后:", removeSuffix(wordList))
//     // 测试 removeAffix
//     fmt.Println("移除前后缀后:", removeAffix(wordList))
//     // 测试 synonymMatch
//     for _, word := range wordList {
//         fmt.Printf("'%s' 在 '%s' 中同义词匹配结果: %v\n", word, title, synonymMatch(word, title))
//     }
// }
