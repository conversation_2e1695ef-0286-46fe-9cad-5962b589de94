package utils

import (
	"strings"
	"unicode"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

// 分词前处理一: 同义词及特殊符号处理
func ReplacePain(input string) string {
	runes := []rune(input)
	var result []rune
	i := 0
	for i < len(runes) {
		if i < len(runes)-1 && runes[i] == '疼' && runes[i+1] == '痛' {
			// 遇到“疼痛”，替换为“痛”
			result = append(result, '痛')
			i += 2
		} else if runes[i] == '疼' {
			// 遇到单个“疼”，替换为“痛”
			result = append(result, '痛')
			i++
		} else if runes[i] == '\n' || runes[i] == ' ' {
			// 遇到换行符或空格，替换为逗号
			result = append(result, ',')
			i++
		} else {
			// 其他情况直接添加当前字符
			result = append(result, runes[i])
			i++
		}
	}
	return string(result)
}

// 分词后处理一: 叠词（重复数字不能处理去重）
func MergeRepeatedChars(strList []string) []string {
	result := make([]string, 0, len(strList))
	for _, s := range strList {
		runes := []rune(s)
		var b strings.Builder
		for i := 0; i < len(runes); {
			if ShouldMerge(runes, i) {
				b.WriteRune(runes[i])
				i += 2
			} else {
				b.WriteRune(runes[i])
				i++
			}
		}
		result = append(result, b.String())
	}
	return result
}

func ShouldMerge(r []rune, i int) bool {
	return i+1 < len(r) &&
		r[i] == r[i+1] &&
		!unicode.IsDigit(r[i])
}

type SegRes struct {
	Keys  []string
	Value []float64
}

// query 分词
func QuerySeg(req *bean.Request, span *span.Span, query string) (SegRes, error) {
	opSpan := span.AddSpan("query分词")
	defer opSpan.Finish()
	// 字母大写
	query = strings.ToUpper(query)
	// 调用 ReplacePain 函数处理查询
	query = ReplacePain(query)
	// 分词
	querykeywords := config.SegBioMedical.CutStop(query)
	// querykeywords := []string{"低", "密度", "脂蛋白", "胆固醇", "是", "偏", "高", "些", "好", "还是", "低", "些", "好", "？"}
	// 调用 MergeRepeatedChars 函数处理叠词
	querykeywords = MergeRepeatedChars(querykeywords)
	// term weight 权重
	keys, values, err := TermWeight(req, opSpan, querykeywords)
	if err != nil {
		return SegRes{}, err
	}

	// 创建一个切片来存储配对后的结果
	var queryKeywords []struct {
		key   string
		value float64
	}

	// 遍历keys和values，创建配对并添加到queryKeywords中
	for i := 0; i < len(keys); i++ {
		queryKeywords = append(queryKeywords, struct {
			key   string
			value float64
		}{key: keys[i], value: values[i]})
	}

	// 返回
	return SegRes{Keys: keys, Value: values}, nil
}

// title 分词
func TitleSeg(req *bean.Request, span *span.Span, title string) (SegRes, error) {
	// 分词加权重计算
	opSpan := span.AddSpan("title分词")
	defer opSpan.Finish()

	titleSegs := strings.Split(title, "_")
	if len(titleSegs) >= 3 {
		// Join all segments except the last two
		title = strings.Join(titleSegs[:len(titleSegs)-2], "_")
	} else if len(titleSegs) == 2 {
		// Only keep the first segment
		title = titleSegs[0]
	}

	// 字母大写
	title = strings.ToUpper(title)
	// 调用 ReplacePain 函数处理查询
	title = ReplacePain(title)
	// 分词
	titlekeywords := config.SegBioMedical.CutStop(title)
	// titlekeywords := []string{"低", "密度", "脂蛋白", "高", "好", "还是", "低", "好"}
	// 调用 MergeRepeatedChars 函数处理叠词
	titlekeywords = MergeRepeatedChars(titlekeywords)
	// term_weight 权重
	keys, values, err := TermWeight(req, opSpan, titlekeywords)
	if err != nil {
		return SegRes{}, err
	}

	// 创建一个切片来存储配对后的结果
	var titleKeywords []struct {
		key   string
		value float64
	}

	// 遍历keys和values，创建配对并添加到titleKeywords中
	for i := 0; i < len(keys); i++ {
		titleKeywords = append(titleKeywords, struct {
			key   string
			value float64
		}{key: keys[i], value: values[i]})
	}

	// 返回
	return SegRes{Keys: keys, Value: values}, nil
}
