package utils

import (
	"fmt"
	"sort"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

func ParseUrl(doc *map[string]any) string {
	// url获取逻辑
	var url string
	protocal, ok := (*doc)["protocol"].(string)
	if !ok {
		protocal = ""
	}
	domain, ok := (*doc)["domain"].(string)
	if !ok {
		domain = ""
	}
	path, ok := (*doc)["path"].(string)
	if !ok {
		path = ""
	}
	url = common.Interface2S((*doc)["url"])
	if len(url) == 0 {
		if strings.Contains(protocal, "://") {
			url = fmt.Sprintf("%s%s%s", protocal, domain, path)
		} else {
			url = fmt.Sprintf("%s://%s%s", protocal, domain, path)
		}
	}
	return url
}

func FinalResultsProcess(span *span.Span, req *bean.Request, rerankScore []*map[string]any, needRank bool) ([]*map[string]any, error) {
	span.AddSpan("最终结果处理")
	defer span.Finish()

	if needRank {
		sort.Slice(rerankScore, func(i, j int) bool {
			score1 := common.Interface2F64((*rerankScore[i])["_rerank_score"])
			score2 := common.Interface2F64((*rerankScore[j])["_rerank_score"])
			if score1 == score2 {
				rank_index1 := common.Interface2I64((*rerankScore[i])["_rank_index"])
				rank_index2 := common.Interface2I64((*rerankScore[j])["_rank_index"])
				return rank_index1 < rank_index2
			}
			return score1 > score2
		})
	}

	index := 1
	for _, r := range rerankScore {
		(*r)["_rerank_index"] = index
		index += 1
	}

	if req.Payload.TopK >= 0 && req.Payload.TopK < len(rerankScore) {
		// 如果limit-1, limit, limit+1重排分数相同，均保留
		limit := req.Payload.TopK

		return rerankScore[:limit], nil
	} else {

		return rerankScore, nil
	}
}
