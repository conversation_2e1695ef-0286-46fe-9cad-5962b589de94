package config

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

const DomainModuleName = "domain"
const DomainTomlName = "domain"

var G_DomainInfoFactory = base_factory.NewFactory(DomainModuleName, NewDomainInfosInstance)
var G_DomainInfoInstance = new(DomainInfo)

type DomainInfo struct {
	base_model.BaseModel
	Id               int      `json:"id"`
	LevelCollections []string `json:"levelCollections"`
	RankCollections  []string `json:"rankCollections"`
}

const (
	DefaultDomainId = 1
)

func NewDomainInfosInstance(option *DomainInfo) (*DomainInfo, error) {
	G_DomainInfoInstance.Id = common.If(option.Id == -1, int(DefaultDomainId), option.Id)
	G_DomainInfoInstance.LevelCollections = option.LevelCollections
	G_DomainInfoInstance.RankCollections = option.RankCollections
	return option, nil
}

var LevelCollectionsMap = map[string][]string{}
var RankCollectionsMap = map[string][]string{}

// func InitDomainInfos() {
// 	domainInfos := GetRerankConfig().DomainInfos
// 	for _, v := range domainInfos["info"] {
// 		dInfo := DomainInfo{}
// 		err := mapstructure.Decode(v, &dInfo)
// 		if err != nil {
// 			panic("domain info parse failed,err: " + err.Error())
// 		}
// 		LevelCollectionsMap[strconv.Itoa(dInfo.Id)] = dInfo.LevelCollections
// 		RankCollectionsMap[strconv.Itoa(dInfo.Id)] = dInfo.RankCollections
// 	}
// }
