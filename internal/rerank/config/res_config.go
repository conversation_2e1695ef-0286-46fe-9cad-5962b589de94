package config

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

const ResModuleName = "res"
const ResTomlName = "res"

var G_ResFactory = base_factory.NewFactory(ResModuleName, NewResInstance)
var G_ResModule = goboot.GetCustomModule(G_ResFactory)

type resConfig struct {
	base_model.BaseModel
	BaseResDir        string     `toml:"resBaseDir" mapstructure:"resBaseDir"`
	StwPath           string     `toml:"stwPath" mapstructure:"stwPath"`
	MedicalDomainPath string     `toml:"medicalDomainPath" mapstructure:"medicalDomainPath"`
	BlockDict         []string   `toml:"blockDict" mapstructure:"blockDict"`
	PrefixDict        []string   `toml:"prefixDict" mapstructure:"prefixDict"`
	SuffixDict        []string   `toml:"suffixDict" mapstructure:"suffixDict"`
	SynonymsDict      [][]string `toml:"synonymsDict" mapstructure:"synonymsDict"`
}

func NewResInstance(option *resConfig) (*resConfig, error) {
	// G_ResInstance.BaseResDir = option.BaseResDir
	// G_ResInstance.StwPath = option.StwPath
	// G_ResInstance.MedicalDomainPath = option.MedicalDomainPath

	return option, nil
}

var StopWords []string
var MedicalDomais []string

func LoadStopWords() error {
	// StwPath := fmt.Sprintf("%s%s", ProjectRootPath, G_ResModule.DefaultInstance().StwPath)
	StwPath := G_ResModule.DefaultInstance().StwPath
	// file, err := os.Open(GetRerankConfig().Res.StwPath)
	file, err := os.Open(StwPath)
	if err != nil {
		return err
	}
	defer func() {
		file.Close()
	}()

	scanner := bufio.NewScanner(file)
	// 逐行读取文件内容
	for scanner.Scan() {
		line := scanner.Text()
		StopWords = append(StopWords, strings.TrimSpace(line))
		// fmt.Println(line)
	}

	// 检查扫描过程中是否发生错误
	if err := scanner.Err(); err != nil {
		return err
	}

	return nil
}

func GetSegDictFiles() []string {
	// fmt.Println(GetResConfig())
	// segDictDir := fmt.Sprintf("%s/%s", G_ResModule.DefaultInstance().BaseResDir, "segwords")
	segDictDir := fmt.Sprintf("%s/%s", G_ResModule.DefaultInstance().BaseResDir, "segwords")
	files, err := os.ReadDir(segDictDir)
	if err != nil {
		panic(err)
	}

	paths := make([]string, len(files))
	for index, file := range files {
		paths[index] = fmt.Sprintf("%s/%s", segDictDir, file.Name())
	}

	return paths
}

func LoadMedicalDomain() error {
	// MedicalDomainPath := fmt.Sprintf("%s%s", ProjectRootPath, G_ResModule.DefaultInstance().MedicalDomainPath)
	MedicalDomainPath := G_ResModule.DefaultInstance().MedicalDomainPath
	// fmt.Println("医疗域名地址:", MedicalDomainPath)
	// file, err := os.Open(GetRerankConfig().Res.MedicalDomainPath)
	file, err := os.Open(MedicalDomainPath)
	if err != nil {
		return err
	}
	defer func() {
		file.Close()
	}()

	scanner := bufio.NewScanner(file)
	// 逐行读取文件内容
	for scanner.Scan() {
		line := scanner.Text()
		MedicalDomais = append(MedicalDomais, strings.TrimSpace(line))
		// fmt.Println(line)
	}

	// 检查扫描过程中是否发生错误
	if err := scanner.Err(); err != nil {
		return err
	}

	return nil
}
