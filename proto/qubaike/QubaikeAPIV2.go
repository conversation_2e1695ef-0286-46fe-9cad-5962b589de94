package proto_qubaike

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	"go.mongodb.org/mongo-driver/bson"
)

var ProtoQuBaikeAPIV2 = pandora.NewPandoraProto[QubaikeRequestPayLoad, QubaikeRespPayLoad]()

type QubaikeRequestPayLoad struct {
	Texts           []string `json:"query"`
	Debug           bool     `json:"debug"`
	NameMatchNum    int      `json:"nameMatchNum"`
	AliasMatchNum   int      `json:"aliasMatchNum"`
	SynonymMatchNum int      `json:"synonymMatchNum"`
	Domain          string   `json:"domain"`
	IndexCode       string   `json:"indexCode"`
	Tag             string   `json:"skynet-tlb-service-tag-selector"`
}

type Entity struct {
	Name       string `json:"name"`
	Type       string `json:"type"`
	Id         int64  `json:"id"`
	Url        string `json:"url"`
	MatchType  string `json:"match_type"`
	MustRecall int32  `json:"mustRecall"`
	Rank       int    `json:"rank"`
}

type Result struct {
	//Query          string    `json:"query"`
	NameMatched    []*Entity `json:"nameMatch"`
	AliasMatched   []*Entity `json:"aliasMatch"`
	SynonymMatched []*Entity `json:"synonymMatch"`
}

type QubaikeRespPayLoad struct {
	Data     []*MongoData   `json:"data"`
	JsonSpan *span.JsonSpan `json:"span,omitempty"`
}

type MongoData struct {
	Query     string    `json:"query"`
	IndexCode string    `json:"indexCode"`
	Docs      []*bson.M `json:"docs"`
	Results   *Result   `json:"results"`
}

type IndexMongoPayLoad struct {
	IndexCode string  `json:"indexCode"`
	IDs       []int64 `json:"ids"`
}

type IndexMongoRespPayLoad struct {
	Docs []bson.M `json:"docs"`
}

type ItemInfo struct {
	Id         int64  `json:"id" bson:"id"`
	Title      string `json:"title" bson:"title"`
	Type       string `json:"type" bson:"type"`
	Url        string `json:"url" bson:"url"`
	Rank       int32  `json:"rank" bson:"rank"`
	MustRecall int32  `json:"mustRecall" bson:"mustRecall" default:"0"`
	Status     int32  `json:"status" bson:"status" default:"0"`
	Index      int    `json:"index" bson:"index" default:"0"`
}

type MatchResult struct {
	NameMatch    []*ItemInfo `json:"name_match,omitempty" bson:"name_match"`
	SynonymMatch []*ItemInfo `json:"synonym_match,omitempty" bson:"synonym_match"`
	AliasMatch   []*ItemInfo `json:"alias_match,omitempty" bson:"alias_match"`
}

type EntityDetail struct {
	Id     int64        `json:"_id" bson:"_id"`
	Name   string       `json:"entity" bson:"entity"`
	Items  *MatchResult `json:"items" bson:"items"`
	Status int32        `json:"status" bson:"status" default:"0"`
	Uts    int64        `json:"update_ts" bson:"update_ts"`
}
