package proto_intent

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

// 意图分类服务对外暴露协议
type ProtoIntentAPIV2Request struct {
	IntentModel    string   `json:"intent_model"`
	ClassifyModel  string   `json:"classify_model"`
	EmbeddingModel string   `json:"embedding_model"`
	Queries        []string `json:"texts,omitempty" json:"query,omitempty"`
}

type ProtoIntentAPIV2Response struct {
	IntentModel    string                        `json:"intent_model"`
	ClassifyModel  string                        `json:"classify_model"`
	EmbeddingModel string                        `json:"embedding_model"`
	Results        []*ProtoIntentAPIV2IntentInfo `json:"results"`
}
type ProtoIntentAPIV2IntentInfo struct {
	Text   string                         `json:"text"`
	Intent []*ProtoIntentAPIV2IntentSlots `json:"intent"`
}

type ProtoIntentAPIV2IntentSlots struct {
	Name   string `json:"name"`
	Weight int
	Slots  []*ProtoIntentAPIV2Slot `json:"slots"`
}

type ProtoIntentAPIV2Slot struct {
	Name  string                       `json:"name"`
	Value []*ProtoIntentAPIV2SlotValue `json:"value"`
}

type ProtoIntentAPIV2SlotValue struct {
	Raw    string `json:"raw"`
	Normal string `json:"normal"`
}

var ProtoIntentAPIV2 = pandora.NewPandoraProto[ProtoIntentAPIV2Request, ProtoIntentAPIV2Response]()

// 意图分类内部调用其他组件协议

// 向量计算
