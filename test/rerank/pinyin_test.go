package main

import (
	"fmt"
	"testing"

	"github.com/mozillazg/go-pinyin"
)

func TestPinyin(t *testing.T) {
	// 创建懒人拼音转换器

	args := pinyin.NewArgs()
	args.Style = pinyin.NORMAL // 无音标模式
	args.Separator = ""        // 禁用分隔符
	args.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)} // 非汉字字符回退策略
	}

	// 执行转换（效果最接近lazy_pinyin）
	result := pinyin.Pinyin("刘德华", args)
	fmt.Println(flatten(result)) // 输出: [zhong xin G o l a n g]

}

// 二维切片转一维
func flatten(input [][]string) []string {
	var res []string
	for _, group := range input {
		res = append(res, group...)
	}
	return res
}
