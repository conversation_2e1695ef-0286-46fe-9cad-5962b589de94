package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"

	test2 "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/test"
)

func TestEmergencyApiV2(t *testing.T) {
	requestData := "{\"header\":{\"traceId\":\"domainTest\",\"appid\":\"hfliang\"},\"payload\":{\"topk\":1,\"texts\":[\"再生医学与精准抗衰临床试验检验平台有什么作用？\"]}}"

	// 发送请求
	resp, err := http.Post(test2.IntentApiV2, "application/json", bytes.NewBuffer([]byte(requestData)))
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应内容:", string(body))
}
