package main

import (
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/app"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/cmd"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/cloud_lock"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func main() {
	//命令行初始化，或取path
	cmd.Init()
	err := goboot.InitFromConfig(cmd.GServerConfig.ConfigPath)
	if err != nil {
		panic(err)
	}
	cloud_lock.CloudLockInit()

	goboot.BootConf().Must()
	goboot.Logger().Must()
	goboot.Elklog().Must()
	goboot.TlbSdk().Must()
	goboot.HttpServer().Must()

	//app 初始化，注册路由
	app.Init()

	goboot.RunServer()

	//退出信号监听
	goboot.Singal(nil, 15*time.Second)
}
