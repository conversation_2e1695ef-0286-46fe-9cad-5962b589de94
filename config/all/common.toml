#recall_vector
[cache]
enabled=true
name = "cache"
shard = 64
life_window = 1              #单位:分钟
clean_window = 1              #单位:分钟
max_entries_in_window = 60000
max_entry_size = 16384

#recall_vector,domain
[[ants_pools]]
enabled = true
name="default"
goroutine_num=8192

[ssmetric]
enabled = true
name = "metrics"
api = ""
sub = ""
appid = ""
endpoint = ""
srv = ""
code = ""
table = ""
domain = ""


[cloud_lock]
enabled=false
# lib 库路径
lib_path = "/opt/jfs/lynxiao-deploy/lynxiao/auth_demo/python-SDK/libdongle.so"
# 授权文件路径
auth_file_path = "/opt/jfs/lynxiao-deploy/lynxiao/dongle"
# 产品名称
product_name = "Lynxiao"
# 版本号，必须是三位，eg：1.0.0
version = "2.1.0"
# 授权码，hasp授权时为空字符串
author_code = ""
