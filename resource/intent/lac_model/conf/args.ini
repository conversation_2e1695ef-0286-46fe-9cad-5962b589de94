[DEFAULT]
default_name = lexer


[NETWORK_CONFIG]
word_emb_dim = 128
grnn_hidden_dim = 128
bigru_num  = 2

[TRAIN_CONFIG]
model = lac
use_cuda = no
random_seed = 0
batch_size = 256
epoch = 2
traindata_shuffle_buffer = 200000
base_learning_rate = 0.001
emb_learning_rate = 1.0
crf_learning_rate = 0.2
cpu_num = 10
model_save_dir = ./lac_save
init_checkpoint = model

[DICT_FILE]
word_dict_path = conf/word.dic
label_dict_path = conf/tag.dic
word_rep_dict_path = conf/q2b.dic
