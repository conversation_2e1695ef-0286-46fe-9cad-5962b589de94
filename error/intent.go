package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

const (

	//向量计算模块
	EmbeddingStartFailed = "向量计算模块启动错误"
	EmbeddingTimeout     = "请求向量计算超时"
	EmbeddingCalcFailed  = "向量计算错误"

	// milvus 查询相关
	MilvusCreatError      = "创建向量库对象错误"
	MilvusSearchError     = "向量库搜索错误"
	MilvusResultCountZero = "向量库搜索结果为空"

	// lac 分词相关
	LacNoInsatance   = "lac分词器未初始化"
	LacInputEmpty    = "lac分词输入为空"
	LacCalcError     = "lac分词计算错误"
	LacTimeoutError  = "lac分词计算超时"
	LacResultLenZero = "lac分词结果为空"
)

var (
	IntentError_EmbeddingStartFailed = errtypes.IntentError.NewSelfError(EmbeddingStartFailed)
	IntentError_EmbeddingTimeout     = errtypes.IntentError.NewSelfError(EmbeddingTimeout)
	IntentError_EmbeddingCalcFailed  = errtypes.IntentError.NewSelfError(EmbeddingCalcFailed)

	IntentError_MilvusCreatError      = errtypes.IntentError.NewSelfError(MilvusCreatError)
	IntentError_MilvusSearchError     = errtypes.IntentError.NewSelfError(MilvusSearchError)
	IntentError_MilvusResultCountZero = errtypes.IntentError.NewSelfError(MilvusResultCountZero)

	IntentError_LacNoInsatance   = errtypes.IntentError.NewSelfError(LacNoInsatance)
	IntentError_LacInputEmpty    = errtypes.IntentError.NewSelfError(LacInputEmpty)
	IntentError_LacCalcError     = errtypes.IntentError.NewSelfError(LacCalcError)
	IntentError_LacTimeoutError  = errtypes.IntentError.NewSelfError(LacTimeoutError)
	IntentError_LacResultLenZero = errtypes.IntentError.NewSelfError(LacResultLenZero)
)
